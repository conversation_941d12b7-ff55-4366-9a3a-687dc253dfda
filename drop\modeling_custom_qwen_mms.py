"""
Custom Qwen-MMS Model Implementation
Combines Qwen2.5-Omni's vision and language components with MMS Wav2Vec2 audio encoder
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import PreTrainedModel, GenerationMixin
from transformers.models.wav2vec2.modeling_wav2vec2 import Wav2Vec2Model
from transformers.modeling_outputs import BaseModelOutputWithPast
from transformers.utils import ModelOutput
from typing import Optional, Tuple, Union, List
import logging
from dataclasses import dataclass

from .configuration_custom_qwen_mms import CustomQwenMMSConfig, CustomQwenMMSAudioConfig
from .weight_loader import HybridWeightLoader

logger = logging.getLogger(__name__)


@dataclass
class CustomQwenMMSModelOutput(ModelOutput):
    """
    Custom output class for Qwen-MMS model
    """
    last_hidden_state: Optional[torch.FloatTensor] = None
    past_key_values: Optional[Tuple[Tuple[torch.FloatTensor]]] = None
    hidden_states: Optional[Tuple[torch.FloatTensor]] = None
    attentions: Optional[Tuple[torch.FloatTensor]] = None
    audio_hidden_states: Optional[torch.FloatTensor] = None
    image_hidden_states: Optional[torch.FloatTensor] = None


@dataclass
class CustomQwenMMSCausalLMOutput(ModelOutput):
    """
    Custom causal LM output class for Qwen-MMS model
    """
    loss: Optional[torch.FloatTensor] = None
    logits: Optional[torch.FloatTensor] = None
    past_key_values: Optional[Tuple[Tuple[torch.FloatTensor]]] = None
    hidden_states: Optional[Tuple[torch.FloatTensor]] = None
    attentions: Optional[Tuple[torch.FloatTensor]] = None
    audio_hidden_states: Optional[torch.FloatTensor] = None
    image_hidden_states: Optional[torch.FloatTensor] = None


class CustomAudioProjector(nn.Module):
    """
    Audio feature projector that adapts MMS output to Qwen input format
    """
    
    def __init__(self, config: CustomQwenMMSAudioConfig):
        super().__init__()
        self.config = config
        
        if config.adapter_type == "linear":
            self.projector = nn.Linear(
                config.mms_hidden_size, 
                config.qwen_output_dim,
                bias=True
            )
        elif config.adapter_type == "mlp":
            self.projector = nn.Sequential(
                nn.Linear(config.mms_hidden_size, config.mms_hidden_size * 2),
                nn.GELU() if config.activation == "gelu" else nn.ReLU(),
                nn.Dropout(config.dropout_rate),
                nn.Linear(config.mms_hidden_size * 2, config.qwen_output_dim)
            )
        else:
            raise ValueError(f"Unsupported adapter type: {config.adapter_type}")
        
        if config.layer_norm:
            self.layer_norm = nn.LayerNorm(config.qwen_output_dim)
        else:
            self.layer_norm = None
    
    def forward(self, audio_features: torch.Tensor) -> torch.Tensor:
        """
        Project audio features from MMS to Qwen format
        
        Args:
            audio_features: [batch_size, seq_len, mms_hidden_size]
        
        Returns:
            projected_features: [batch_size, seq_len, qwen_output_dim]
        """
        projected = self.projector(audio_features)
        
        if self.layer_norm is not None:
            projected = self.layer_norm(projected)
        
        return projected


class CustomQwenMMSPreTrainedModel(PreTrainedModel):
    """
    Base class for Custom Qwen-MMS models
    """
    config_class = CustomQwenMMSConfig
    base_model_prefix = "custom_qwen_mms"
    supports_gradient_checkpointing = True
    _no_split_modules = ["CustomAudioProjector"]
    _skip_keys_device_placement = "past_key_values"
    
    def _init_weights(self, module):
        """Initialize weights"""
        std = getattr(self.config, "initializer_range", 0.02)
        
        if isinstance(module, nn.Linear):
            module.weight.data.normal_(mean=0.0, std=std)
            if module.bias is not None:
                module.bias.data.zero_()
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)


class CustomQwenMMSThinkerForConditionalGeneration(CustomQwenMMSPreTrainedModel):
    """
    Custom Qwen-MMS Thinker model for conditional generation
    This is the core model that replaces Qwen's audio encoder with MMS
    """
    
    def __init__(self, config: CustomQwenMMSConfig):
        super().__init__(config)
        self.config = config
        
        # Initialize audio projector
        audio_config = CustomQwenMMSAudioConfig(
            mms_hidden_size=config.audio_adapter_hidden_size,
            qwen_output_dim=config.qwen_audio_output_dim
        )
        self.audio_tower = None  # Will be MMS Wav2Vec2Model
        self.audio_projector = CustomAudioProjector(audio_config)

        # Initialize components (will be loaded from pretrained)
        self.visual = None       # Will be Qwen visual encoder
        self.model = None        # Will be Qwen language model
        self.lm_head = None      # Will be Qwen LM head

        # Special tokens
        self.audio_token_id = config.audio_token_id
        self.video_token_id = config.video_token_id
        self.image_token_id = config.image_token_id
        
        # Initialize weights
        self.post_init()
    
    @classmethod
    def from_pretrained_hybrid(
        cls,
        qwen_omni_path: str,
        mms_wav2vec2_path: str,
        config: Optional[CustomQwenMMSConfig] = None,
        torch_dtype=torch.float16,
        device_map="auto",
        **kwargs
    ):
        """
        Load model with hybrid weights from Qwen-Omni and MMS
        """
        if config is None:
            # Create default config
            config = CustomQwenMMSConfig(
                qwen_omni_path=qwen_omni_path,
                mms_wav2vec2_path=mms_wav2vec2_path
            )
        
        # Initialize model
        model = cls(config)
        
        # Load hybrid weights
        weight_loader = HybridWeightLoader(model)
        loading_stats = weight_loader.load_hybrid_weights(
            qwen_omni_path=qwen_omni_path,
            mms_wav2vec2_path=mms_wav2vec2_path,
            torch_dtype=torch_dtype,
            device_map=device_map
        )
        
        logger.info(f"Model loaded successfully. Stats: {loading_stats}")
        return model
    
    def get_audio_features(self, audio_values: torch.Tensor) -> torch.Tensor:
        """
        Extract audio features using MMS encoder and project to Qwen format
        
        Args:
            audio_values: Raw audio waveform [batch_size, sequence_length]
        
        Returns:
            audio_features: Projected audio features [batch_size, seq_len, hidden_size]
        """
        if self.audio_tower is None:
            raise ValueError("Audio tower not loaded. Use from_pretrained_hybrid() to load the model.")
        
        # Extract features using MMS
        audio_outputs = self.audio_tower(audio_values)
        audio_features = audio_outputs.last_hidden_state  # [batch_size, seq_len, 1280]
        
        # Project to Qwen format
        projected_features = self.audio_projector(audio_features)  # [batch_size, seq_len, 3584]
        
        return projected_features
    
    def get_image_features(self, pixel_values: torch.Tensor) -> torch.Tensor:
        """
        Extract image features using Qwen visual encoder
        
        Args:
            pixel_values: Image tensor [batch_size, channels, height, width]
        
        Returns:
            image_features: Image features [batch_size, seq_len, hidden_size]
        """
        if self.visual is None:
            raise ValueError("Visual encoder not loaded. Use from_pretrained_hybrid() to load the model.")
        
        # Use Qwen's visual encoder
        image_outputs = self.visual(pixel_values)
        
        # The exact output format depends on Qwen's visual encoder implementation
        # This might need adjustment based on the actual Qwen visual encoder
        if hasattr(image_outputs, 'last_hidden_state'):
            return image_outputs.last_hidden_state
        else:
            return image_outputs
    
    def forward(
        self,
        input_ids: Optional[torch.LongTensor] = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[List[torch.FloatTensor]] = None,
        inputs_embeds: Optional[torch.FloatTensor] = None,
        audio_values: Optional[torch.FloatTensor] = None,
        pixel_values: Optional[torch.FloatTensor] = None,
        labels: Optional[torch.LongTensor] = None,
        use_cache: Optional[bool] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
        **kwargs
    ) -> Union[Tuple, CustomQwenMMSCausalLMOutput]:
        """
        Forward pass of the Custom Qwen-MMS model
        """
        if self.model is None:
            raise ValueError("Language model not loaded. Use from_pretrained_hybrid() to load the model.")
        
        output_attentions = output_attentions if output_attentions is not None else self.config.output_attentions
        output_hidden_states = output_hidden_states if output_hidden_states is not None else self.config.output_hidden_states
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict
        
        # Process multimodal inputs
        audio_features = None
        image_features = None
        
        if audio_values is not None:
            audio_features = self.get_audio_features(audio_values)
        
        if pixel_values is not None:
            image_features = self.get_image_features(pixel_values)
        
        # Get input embeddings
        if inputs_embeds is None:
            inputs_embeds = self.model.embed_tokens(input_ids)
        
        # Merge multimodal features with text embeddings
        if audio_features is not None or image_features is not None:
            inputs_embeds = self._merge_multimodal_embeddings(
                inputs_embeds, input_ids, audio_features, image_features
            )
        
        # Forward through language model
        outputs = self.model(
            inputs_embeds=inputs_embeds,
            attention_mask=attention_mask,
            position_ids=position_ids,
            past_key_values=past_key_values,
            use_cache=use_cache,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=True,
            **kwargs
        )
        
        # Compute logits
        hidden_states = outputs.last_hidden_state
        logits = self.lm_head(hidden_states)
        
        # Compute loss if labels provided
        loss = None
        if labels is not None:
            # Shift labels for causal LM
            shift_logits = logits[..., :-1, :].contiguous()
            shift_labels = labels[..., 1:].contiguous()
            
            # Compute cross entropy loss
            loss_fct = nn.CrossEntropyLoss()
            loss = loss_fct(shift_logits.view(-1, shift_logits.size(-1)), shift_labels.view(-1))
        
        if not return_dict:
            output = (logits,) + outputs[1:]
            return (loss,) + output if loss is not None else output
        
        return CustomQwenMMSCausalLMOutput(
            loss=loss,
            logits=logits,
            past_key_values=outputs.past_key_values,
            hidden_states=outputs.hidden_states,
            attentions=outputs.attentions,
            audio_hidden_states=audio_features,
            image_hidden_states=image_features,
        )

    def _merge_multimodal_embeddings(
        self,
        inputs_embeds: torch.Tensor,
        input_ids: torch.Tensor,
        audio_features: Optional[torch.Tensor] = None,
        image_features: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """
        Merge multimodal features with text embeddings based on special tokens
        """
        if audio_features is None and image_features is None:
            return inputs_embeds

        batch_size, seq_len, hidden_size = inputs_embeds.shape

        # Process audio features
        if audio_features is not None and input_ids is not None:
            audio_mask = (input_ids == self.audio_token_id)
            if audio_mask.any():
                # Replace audio token embeddings with audio features
                audio_features_flat = audio_features.view(-1, hidden_size)
                audio_positions = audio_mask.nonzero(as_tuple=False)

                for i, (batch_idx, seq_idx) in enumerate(audio_positions):
                    if i < audio_features_flat.shape[0]:
                        inputs_embeds[batch_idx, seq_idx] = audio_features_flat[i]

        # Process image features
        if image_features is not None and input_ids is not None:
            image_mask = (input_ids == self.image_token_id)
            if image_mask.any():
                # Replace image token embeddings with image features
                image_features_flat = image_features.view(-1, hidden_size)
                image_positions = image_mask.nonzero(as_tuple=False)

                for i, (batch_idx, seq_idx) in enumerate(image_positions):
                    if i < image_features_flat.shape[0]:
                        inputs_embeds[batch_idx, seq_idx] = image_features_flat[i]

        return inputs_embeds

    def prepare_inputs_for_generation(
        self,
        input_ids,
        past_key_values=None,
        attention_mask=None,
        inputs_embeds=None,
        audio_values=None,
        pixel_values=None,
        **kwargs
    ):
        """Prepare inputs for generation"""
        if past_key_values is not None:
            input_ids = input_ids[:, -1:]

        # If we have past_key_values, we don't need to process multimodal inputs again
        if past_key_values is not None:
            audio_values = None
            pixel_values = None

        model_inputs = {
            "input_ids": input_ids,
            "past_key_values": past_key_values,
            "attention_mask": attention_mask,
            "inputs_embeds": inputs_embeds,
            "audio_values": audio_values,
            "pixel_values": pixel_values,
        }

        return model_inputs


class CustomQwenMMSForConditionalGeneration(CustomQwenMMSPreTrainedModel, GenerationMixin):
    """
    Complete Custom Qwen-MMS model for conditional generation
    This wraps the thinker model and adds generation capabilities
    """

    def __init__(self, config: CustomQwenMMSConfig):
        super().__init__(config)
        self.thinker = CustomQwenMMSThinkerForConditionalGeneration(config)

        # Copy important attributes
        self.vocab_size = config.vocab_size
        self.pad_token_id = getattr(config, 'pad_token_id', 0)

        self.post_init()

    @classmethod
    def from_pretrained_hybrid(
        cls,
        qwen_omni_path: str,
        mms_wav2vec2_path: str,
        config: Optional[CustomQwenMMSConfig] = None,
        **kwargs
    ):
        """Load model with hybrid weights"""
        if config is None:
            config = CustomQwenMMSConfig(
                qwen_omni_path=qwen_omni_path,
                mms_wav2vec2_path=mms_wav2vec2_path
            )

        model = cls(config)

        # Load thinker with hybrid weights
        model.thinker = CustomQwenMMSThinkerForConditionalGeneration.from_pretrained_hybrid(
            qwen_omni_path=qwen_omni_path,
            mms_wav2vec2_path=mms_wav2vec2_path,
            config=config,
            **kwargs
        )

        return model

    def forward(self, **kwargs):
        """Forward pass through thinker"""
        return self.thinker(**kwargs)

    def generate(self, **kwargs):
        """Generate text using the thinker model"""
        return super().generate(**kwargs)

    def prepare_inputs_for_generation(self, **kwargs):
        """Prepare inputs for generation"""
        return self.thinker.prepare_inputs_for_generation(**kwargs)

    def get_audio_features(self, audio_values):
        """Get audio features"""
        return self.thinker.get_audio_features(audio_values)

    def get_image_features(self, pixel_values):
        """Get image features"""
        return self.thinker.get_image_features(pixel_values)
