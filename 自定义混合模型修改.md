现在需要用llamafactory这个框架来支持一个修改后的Qwen2_5_omni模型，像使用原来的Qwen2_5_omni模型一样来使用修改后的模型。
注意，需要列出计划清单Tasks逐步执行。
1. 首先了解模型结构，你可以看 `模型结构和示例代码.md`。
2. `D:\vscode_programs\LLaMA-Factory\transformers-4.55.0\src\transformers\models\qwen2_5_omni\modeling_qwen2_5_omni.py` 中我添加了一些注释，这些注释是用Qwen2_5_omni推理调试过程中添加的。
3. 我们需要在 `modeling_qwen2_5_omni.py` 上直接修改，在 Qwen2_5OmniThinkerForConditionalGeneration 类中，除了audio_tower,我们再添加一个MMS的音频编码器（即wav2vec2，一个Wav2Vec2Model）。要实现双音频编码器，我们要完成：
    - MMS模型加载，得到其中的 wav2vec2
    - 在 Qwen2_5OmniForConditionalGeneration 中创建一个和audio_tower平级的音频编码器，我们直接命名为 wav2vec2， 但是需要在最后添加一个可学习的线性层（类似audio_tower.proj），将wav2vec2的输出投影到audio_tower的输入维度上（1280—>3584）
    - 修改 Qwen2_5OmniThinkerForConditionalGeneration 的 get_audio_features 方法，这个方法是forawrd过程中对音频信号进行编码，得到音频特征的过程。这个方法的输入是音频信号，wav2vec2需要的输入也是音频信号，我们首先用wav2vec2编码，得到音频特征1， 然后源码中的`audio_outputs = self.audio_tower(...)`，audio_tower需要的输入是mel-spectrogram，得到音频特征2。我们需要将音频特征1和音频特征2以一个可学习的权重因子alpha进行线性组合，得到最终的音频特征。
    - 还有其他地方需要修改的也列出来给我审核。
