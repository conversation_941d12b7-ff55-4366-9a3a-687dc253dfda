# Custom Qwen-MMS Model Implementation
# This package implements a custom Qwen-Omni model with MMS audio encoder replacement

from .modeling_custom_qwen_mms import (
    CustomQwenMMSConfig,
    CustomQwenMMSForConditionalGeneration,
    CustomQwenMMSThinkerForConditionalGeneration,
)

from .processing_custom_qwen_mms import CustomQwenMMSProcessor

from .weight_loader import (
    HybridWeightLoader,
    WeightMapper,
    DimensionAdapter,
)

__all__ = [
    "CustomQwenMMSConfig",
    "CustomQwenMMSForConditionalGeneration", 
    "CustomQwenMMSThinkerForConditionalGeneration",
    "CustomQwenMMSProcessor",
    "HybridWeightLoader",
    "WeightMapper",
    "DimensionAdapter",
]
