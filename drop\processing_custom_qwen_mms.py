"""
Custom Qwen-MMS Processor
Inherits from Qwen2_5OmniProcessor and only replaces audio processing with MMS
"""

import torch
import numpy as np
from transformers.models.wav2vec2.feature_extraction_wav2vec2 import Wav2Vec2FeatureExtractor
from transformers.models.qwen2_5_omni.processing_qwen2_5_omni import Qwen2_5OmniProcessor
from typing import List, Dict, Any, Optional, Union
import logging

logger = logging.getLogger(__name__)


class CustomQwenMMSProcessor(Qwen2_5OmniProcessor):
    """
    Custom processor for Qwen-MMS model
    Inherits from Qwen2_5OmniProcessor and only replaces audio feature extractor with MMS
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Replace feature_extractor with MMS Wav2Vec2FeatureExtractor if needed
        if hasattr(self, 'feature_extractor') and self.feature_extractor is not None:
            # Keep the original for compatibility, but we'll use audio_feature_extractor for MMS
            pass
    
    @classmethod
    def from_pretrained_hybrid(
        cls,
        qwen_omni_path: str,
        mms_wav2vec2_path: str,
        **kwargs
    ):
        """
        Create processor from Qwen-Omni and MMS pretrained models
        """
        # Load base Qwen processor
        qwen_processor = Qwen2_5OmniProcessor.from_pretrained(qwen_omni_path, **kwargs)

        # Load MMS feature extractor for audio processing
        mms_feature_extractor = Wav2Vec2FeatureExtractor.from_pretrained(mms_wav2vec2_path)

        # Create custom processor by copying the Qwen processor and replacing audio component
        processor = cls.__new__(cls)
        processor.__dict__.update(qwen_processor.__dict__)

        # Replace audio feature extractor with MMS
        processor.audio_feature_extractor = mms_feature_extractor

        return processor

