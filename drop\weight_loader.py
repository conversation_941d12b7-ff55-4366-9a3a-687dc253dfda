"""
Hybrid Weight Loader for Custom Qwen-MMS Model
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import (
    Qwen2_5OmniForConditionalGeneration,
    Qwen2_5OmniThinkerForConditionalGeneration,
    Wav2Vec2Model,
    Wav2Vec2Config
)
from typing import Dict, Tuple, Optional, List
import logging
from pathlib import Path
import json

logger = logging.getLogger(__name__)


class WeightMapper:
    """权重映射和过滤工具"""
    
    @staticmethod
    def extract_qwen_weights(state_dict: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """从Qwen权重中提取非audio部分"""
        filtered_weights = {}
        
        # 定义要保留的权重前缀
        keep_prefixes = [
            "visual.",      # vision encoder
            "model.",       # language model
            "lm_head.",     # language model head
        ]
        
        # 定义要跳过的权重前缀
        skip_prefixes = [
            "audio_tower.",  # 跳过原有的audio encoder
        ]
        
        for key, value in state_dict.items():
            # 检查是否应该保留
            should_keep = any(key.startswith(prefix) for prefix in keep_prefixes)
            should_skip = any(key.startswith(prefix) for prefix in skip_prefixes)
            
            if should_keep and not should_skip:
                filtered_weights[key] = value
                logger.debug(f"保留权重: {key}")
            else:
                logger.debug(f"跳过权重: {key}")
        
        return filtered_weights
    
    @staticmethod
    def map_mms_weights(state_dict: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """将MMS权重映射到目标模型结构 NOTE 函数未使用"""
        mapped_weights = {}
        
        # 定义权重映射规则
        mapping_rules = {
            # Feature Extractor
            "feature_extractor.conv_layers": "audio_tower.feature_extractor.conv_layers",
            
            # Feature Projection
            "feature_projection.layer_norm": "audio_tower.feature_projection.layer_norm",
            "feature_projection.projection": "audio_tower.feature_projection.projection",
            "feature_projection.dropout": "audio_tower.feature_projection.dropout",
            
            # Encoder
            "encoder.pos_conv_embed": "audio_tower.encoder.pos_conv_embed",
            "encoder.layer_norm": "audio_tower.encoder.layer_norm",
            "encoder.dropout": "audio_tower.encoder.dropout",
            "encoder.layers": "audio_tower.encoder.layers",
        }
        
        for old_key, value in state_dict.items():
            # 查找匹配的映射规则
            new_key = None
            for old_prefix, new_prefix in mapping_rules.items():
                if old_key.startswith(old_prefix):
                    new_key = old_key.replace(old_prefix, new_prefix)
                    break
            
            if new_key:
                mapped_weights[new_key] = value
                logger.debug(f"映射权重: {old_key} -> {new_key}")
            else:
                logger.debug(f"跳过MMS权重: {old_key}")
        
        return mapped_weights


class DimensionAdapter:
    """维度适配器"""
    
    @staticmethod
    def adapt_linear_layer(
        source_weight: torch.Tensor,
        target_shape: Tuple[int, int],
        method: str = "interpolate"
    ) -> torch.Tensor:
        """适配线性层权重维度 NOTE 函数未使用"""
        
        if source_weight.shape == target_shape:
            return source_weight
        
        logger.info(f"适配维度: {source_weight.shape} -> {target_shape}")
        
        if method == "interpolate":
            # 使用插值方法
            return F.interpolate(
                source_weight.unsqueeze(0).unsqueeze(0),
                size=target_shape,
                mode='bilinear',
                align_corners=False
            ).squeeze(0).squeeze(0)
            
        elif method == "truncate_pad":
            # 截断或填充方法
            adapted = torch.zeros(target_shape, dtype=source_weight.dtype)
            
            min_rows = min(source_weight.shape[0], target_shape[0])
            min_cols = min(source_weight.shape[1], target_shape[1])
            
            adapted[:min_rows, :min_cols] = source_weight[:min_rows, :min_cols]
            
            return adapted
            
        elif method == "random_projection":
            # 随机投影方法
            if source_weight.shape[1] != target_shape[1]:
                # 需要投影
                projection_matrix = torch.randn(source_weight.shape[1], target_shape[1])
                projection_matrix = F.normalize(projection_matrix, dim=0)
                projected = source_weight @ projection_matrix
            else:
                projected = source_weight
            
            # 处理行维度
            if projected.shape[0] != target_shape[0]:
                return DimensionAdapter.adapt_linear_layer(
                    projected, target_shape, method="truncate_pad"
                )
            
            return projected
        
        else:
            raise ValueError(f"Unknown adaptation method: {method}")


class HybridWeightLoader:
    """混合权重加载器"""
    
    def __init__(self, model):
        self.model = model
        self.loading_stats = {
            "qwen_loaded": 0,
            "mms_loaded": 0,
            "adaptation_initialized": 0,
            "total_params": 0,
            "missing_keys": [],
            "unexpected_keys": []
        }
    
    def load_hybrid_weights(
        self,
        qwen_omni_path: str,
        mms_wav2vec2_path: str,
        torch_dtype=torch.float16,
        device_map="auto"
    ):
        """
        分层加载预训练权重的核心方法
        """
        logger.info("开始分层加载预训练权重...")
        
        try:
            # Step 1: 分别加载各组件的权重
            self._load_qwen_components(qwen_omni_path, torch_dtype, device_map)
            self._load_mms_audio_encoder(mms_wav2vec2_path, torch_dtype, device_map)
            self._initialize_adaptation_layers()
            
            # Step 2: 最终验证
            self._final_validation()
            
            logger.info("分层权重加载完成!")
            return self.loading_stats
            
        except Exception as e:
            logger.error(f"权重加载失败: {e}")
            raise
    
    def _load_qwen_components(self, qwen_path: str, torch_dtype, device_map):
        """加载Qwen_Omni的vision、language和其他组件"""
        logger.info(f"从 {qwen_path} 加载Qwen组件...")

        # 1. 加载完整的Qwen模型
        qwen_model = Qwen2_5OmniForConditionalGeneration.from_pretrained(
            qwen_path,
            torch_dtype=torch_dtype,
            device_map="cpu"  # 先加载到CPU
        )

        # 2. 从thinker中提取需要的组件
        thinker = qwen_model.thinker
        logger.info(f"Qwen模型结构: {type(qwen_model)}")
        logger.info(f"Thinker结构: {type(thinker)}")

        if hasattr(thinker, 'visual'):
            self.model.visual = thinker.visual
            logger.info("✓ Visual组件加载完成")

        if hasattr(thinker, 'model'):
            self.model.model = thinker.model  # language model
            logger.info("✓ Language model组件加载完成")

        if hasattr(thinker, 'lm_head'):
            self.model.lm_head = thinker.lm_head
            logger.info("✓ LM head组件加载完成")
        
        # 3. 复制其他必要属性
        if hasattr(thinker, 'vocab_size'):
            self.model.vocab_size = thinker.vocab_size
        elif hasattr(qwen_model, 'vocab_size'):
            self.model.vocab_size = qwen_model.vocab_size

        if hasattr(thinker, 'pad_token_id'):
            self.model.pad_token_id = thinker.pad_token_id
        elif hasattr(qwen_model, 'pad_token_id'):
            self.model.pad_token_id = qwen_model.pad_token_id
        
        # 4. 移动到目标设备
        if device_map != "cpu":
            if hasattr(self.model, 'visual'):
                self.model.visual = self.model.visual.to(device_map)
            if hasattr(self.model, 'model'):
                self.model.model = self.model.model.to(device_map)
            if hasattr(self.model, 'lm_head'):
                self.model.lm_head = self.model.lm_head.to(device_map)
        
        self.loading_stats["qwen_loaded"] = sum(p.numel() for p in qwen_model.parameters())
        logger.info("Qwen组件加载完成")
        
        # 5. 清理内存
        del qwen_model
        torch.cuda.empty_cache()
    
    def _load_mms_audio_encoder(self, mms_path: str, torch_dtype, device_map):
        """加载MMS的audio encoder"""
        logger.info(f"从 {mms_path} 加载MMS audio encoder...")

        # 1. 加载MMS模型
        mms_model = Wav2Vec2Model.from_pretrained(
            mms_path,
            torch_dtype=torch_dtype,
            device_map="cpu"
        )

        # 2. 验证MMS模型结构
        logger.info(f"MMS模型结构: {type(mms_model)}")
        logger.info(f"MMS配置: hidden_size={mms_model.config.hidden_size}")

        # 3. 确保MMS输出维度正确 (应该是1280)
        expected_mms_dim = 1280
        actual_mms_dim = mms_model.config.hidden_size
        if actual_mms_dim != expected_mms_dim:
            logger.warning(f"MMS维度不匹配: 期望{expected_mms_dim}, 实际{actual_mms_dim}")

        # 4. 将MMS模型赋值给audio_tower
        self.model.audio_tower = mms_model

        # 5. 移动到目标设备
        if device_map != "cpu":
            self.model.audio_tower = self.model.audio_tower.to(device_map)

        self.loading_stats["mms_loaded"] = sum(p.numel() for p in mms_model.parameters())
        logger.info(f"✓ MMS audio encoder加载完成，参数量: {self.loading_stats['mms_loaded']:,}")

        # 6. 清理内存
        torch.cuda.empty_cache()
    
    def _initialize_adaptation_layers(self):
        """初始化适配层权重"""
        logger.info("初始化适配层...")

        if hasattr(self.model, 'audio_projector'):
            # 递归初始化audio_projector中的所有线性层
            def init_linear_layers(module):
                if isinstance(module, nn.Linear):
                    nn.init.xavier_uniform_(module.weight)
                    if module.bias is not None:
                        nn.init.zeros_(module.bias)
                    return module.weight.numel()
                elif isinstance(module, nn.Sequential):
                    total_params = 0
                    for layer in module:
                        total_params += init_linear_layers(layer)
                    return total_params
                else:
                    return 0

            total_params = init_linear_layers(self.model.audio_projector)
            self.loading_stats["adaptation_initialized"] = total_params
            logger.info(f"✓ Audio projector初始化完成，参数量: {total_params:,}")

        logger.info("适配层初始化完成")
    
    def _final_validation(self):
        """最终验证"""
        logger.info("进行最终验证...")
        
        # 统计总参数量
        self.loading_stats["total_params"] = sum(p.numel() for p in self.model.parameters())
        
        # 验证关键组件
        required_components = ['audio_tower', 'visual', 'model', 'lm_head']
        missing_components = []
        
        for component in required_components:
            if not hasattr(self.model, component) or getattr(self.model, component) is None:
                missing_components.append(component)
        
        if missing_components:
            raise ValueError(f"缺少关键组件: {missing_components}")
        
        logger.info("✓ 所有组件验证通过")
        logger.info(f"总参数量: {self.loading_stats['total_params']:,}")
