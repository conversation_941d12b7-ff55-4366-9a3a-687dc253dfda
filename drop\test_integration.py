"""
Test script to verify Custom Qwen-MMS integration with LLaMA-Factory
"""

import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_imports():
    """Test if all required modules can be imported"""
    logger.info("Testing imports...")
    
    try:
        # Test custom model imports
        from drop import (
            CustomQwenMMSConfig,
            CustomQwenMMSForConditionalGeneration,
            CustomQwenMMSProcessor,
            HybridWeightLoader
        )
        logger.info("✅ Custom model imports successful")
        
        # Test LLaMA-Factory imports
        import llamafactory
        from llamafactory.data.template import get_template
        from llamafactory.extras.constants import MODELS
        logger.info("✅ LLaMA-Factory imports successful")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import failed: {e}")
        return False


def test_model_registration():
    """Test if custom model is registered with LLaMA-Factory"""
    logger.info("Testing model registration...")
    
    try:
        from llamafactory.extras.constants import MODELS
        
        # Check if custom model is registered
        custom_models = [name for name in MODELS.keys() if "Custom-Qwen-MMS" in name]
        
        if custom_models:
            logger.info(f"✅ Found registered custom models: {custom_models}")
            return True
        else:
            logger.error("❌ Custom models not found in MODELS registry")
            return False
            
    except Exception as e:
        logger.error(f"❌ Model registration test failed: {e}")
        return False


def test_template_registration():
    """Test if custom template is registered"""
    logger.info("Testing template registration...")
    
    try:
        from llamafactory.data.template import get_template
        
        # Try to get custom template
        template = get_template("custom_qwen_mms")
        
        if template:
            logger.info("✅ Custom template registered successfully")
            return True
        else:
            logger.error("❌ Custom template not found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Template registration test failed: {e}")
        return False


def test_plugin_registration():
    """Test if custom plugin is registered"""
    logger.info("Testing plugin registration...")
    
    try:
        from llamafactory.data.mm_plugin import get_mm_plugin
        
        # Try to get custom plugin
        plugin = get_mm_plugin("custom_qwen_mms")
        
        if plugin:
            logger.info("✅ Custom plugin registered successfully")
            return True
        else:
            logger.error("❌ Custom plugin not found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Plugin registration test failed: {e}")
        return False


def test_model_loading():
    """Test basic model loading (without actual weights)"""
    logger.info("Testing model loading...")
    
    try:
        from drop import CustomQwenMMSConfig, CustomQwenMMSForConditionalGeneration
        
        # Create a minimal config for testing
        config = CustomQwenMMSConfig(
            qwen_omni_path="dummy",
            mms_wav2vec2_path="dummy",
            audio_adapter_hidden_size=1280,
            qwen_audio_output_dim=3584,
        )
        
        # Test config creation
        logger.info("✅ Model configuration created successfully")
        
        # Note: We don't actually load weights here to avoid downloading large models
        logger.info("✅ Model loading test passed (config level)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model loading test failed: {e}")
        return False


def test_processor_creation():
    """Test processor creation"""
    logger.info("Testing processor creation...")
    
    try:
        from drop import CustomQwenMMSProcessor
        
        # Test processor class exists and can be imported
        logger.info("✅ Processor class imported successfully")
        
        # Note: We don't actually create a processor here to avoid downloading models
        logger.info("✅ Processor creation test passed (import level)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Processor creation test failed: {e}")
        return False


def test_llamafactory_integration():
    """Test LLaMA-Factory integration setup"""
    logger.info("Testing LLaMA-Factory integration...")
    
    try:
        from drop.llamafactory_integration import setup_llamafactory_integration
        
        # Run integration setup
        setup_llamafactory_integration()
        logger.info("✅ LLaMA-Factory integration setup successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ LLaMA-Factory integration test failed: {e}")
        return False


def run_all_tests():
    """Run all integration tests"""
    logger.info("🚀 Starting Custom Qwen-MMS integration tests...")
    
    tests = [
        ("Import Test", test_imports),
        ("Model Registration Test", test_model_registration),
        ("Template Registration Test", test_template_registration),
        ("Plugin Registration Test", test_plugin_registration),
        ("Model Loading Test", test_model_loading),
        ("Processor Creation Test", test_processor_creation),
        ("LLaMA-Factory Integration Test", test_llamafactory_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Custom Qwen-MMS integration is working correctly.")
        return True
    else:
        logger.error(f"💥 {total - passed} tests failed. Please check the installation.")
        return False


def main():
    """Main test function"""
    success = run_all_tests()
    
    if success:
        logger.info("\n📋 Next steps:")
        logger.info("1. Try the example usage: python example_usage.py")
        logger.info("2. Run training: bash scripts/train_custom_qwen_mms.sh")
        logger.info("3. Run inference: bash scripts/infer_custom_qwen_mms.sh")
        logger.info("4. Start web UI: bash scripts/webui_custom_qwen_mms.sh")
    else:
        logger.info("\n🔧 Troubleshooting:")
        logger.info("1. Ensure LLaMA-Factory is properly installed")
        logger.info("2. Run: python install_integration.py")
        logger.info("3. Restart your Python environment")
        logger.info("4. Check the logs above for specific error messages")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
